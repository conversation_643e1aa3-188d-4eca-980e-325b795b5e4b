#!/usr/bin/env python3
"""
Test script for the 2FA disable request endpoint
"""

import requests
import json

def test_request_disable_2fa():
    """Test the /api/auth/request-disable-2fa endpoint"""
    
    url = "http://localhost:3002/api/auth/request-disable-2fa"
    
    # Test data
    data = {
        "email": "<EMAIL>",
        "reason": "recovery_codes_exhausted"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    print("🧪 Testing /api/auth/request-disable-2fa endpoint...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Request failed!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection Error: {e}")
        print("Is the server running on port 3002?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_request_disable_2fa()
