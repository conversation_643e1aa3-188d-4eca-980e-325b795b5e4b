#!/usr/bin/env python3
"""
Test 2FA Disable and Recovery Code Cleanup
This script tests the specific requirement: cleanup recovery codes when 2<PERSON> is disabled
"""

import requests
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'masonite-backend-clean'))

# Configuration
BASE_URL = 'http://localhost:8001/api'
EMAIL = '<EMAIL>'
PASSWORD = 'SecurePass123!'

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request with error handling"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        
        print(f"📊 {method.upper()} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"   Response: {json.dumps(response_data, indent=2)}")
            return response.status_code, response_data
        else:
            print(f"   Response: {response.text}")
            return response.status_code, {'message': response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return 0, {'error': str(e)}

def check_user_2fa_status():
    """Check user's 2FA status directly in database"""
    try:
        from app.models.User import User
        user = User.where('email', EMAIL).first()
        if user:
            print(f"🔍 Database Check:")
            print(f"   Email: {user.email}")
            print(f"   2FA Enabled: {user.two_factor_enabled}")
            print(f"   2FA Secret exists: {bool(user.two_factor_secret)}")
            print(f"   Backup codes remaining: {getattr(user, 'backup_codes_remaining', 0)}")
            print(f"   Backup code 1 exists: {bool(getattr(user, 'backup_code_1', None))}")
            print(f"   Backup code 2 exists: {bool(getattr(user, 'backup_code_2', None))}")
            print(f"   Backup code 3 exists: {bool(getattr(user, 'backup_code_3', None))}")
            return user
        else:
            print(f"❌ User not found: {EMAIL}")
            return None
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return None

def manually_enable_2fa():
    """Manually enable 2FA for testing purposes"""
    try:
        from app.models.User import User
        user = User.where('email', EMAIL).first()
        if user:
            # Set 2FA as enabled with the secret from the setup
            user.two_factor_enabled = True
            user.save()
            print(f"✅ Manually enabled 2FA for user: {user.email}")
            return True
        return False
    except Exception as e:
        print(f"❌ Failed to manually enable 2FA: {e}")
        return False

def test_2fa_disable_cleanup():
    """Test the main requirement: 2FA disable cleans up recovery codes"""
    print("🧪 Testing 2FA Disable and Recovery Code Cleanup")
    print("=" * 60)
    
    # Step 1: Check initial state
    print("\n🔍 Step 1: Check initial database state")
    user = check_user_2fa_status()
    if not user:
        return False
    
    # Step 2: Ensure 2FA is disabled initially for clean test
    print("\n🔧 Step 2: Ensure clean starting state")
    try:
        from app.models.User import User
        user_obj = User.where('email', EMAIL).first()
        if user_obj:
            user_obj.two_factor_enabled = False
            user_obj.two_factor_secret = None
            user_obj.backup_codes_remaining = 0
            user_obj.backup_code_1 = None
            user_obj.backup_code_2 = None
            user_obj.backup_code_3 = None
            user_obj.save()
            print("✅ Reset user to clean state")
    except Exception as e:
        print(f"❌ Failed to reset user state: {e}")
        return False
        
    # Step 3: Login to get token (without 2FA since we disabled it)
    print("\n🔑 Step 3: Login to get access token")
    status, response = make_request('POST', '/auth/login', {
        'email': EMAIL,
        'password': PASSWORD
    })
    
    if status != 200:
        print(f"❌ Login failed with status {status}")
        return False
        
    token = response.get('token')
    headers = {'Authorization': f'Bearer {token}'}
    print(f"✅ Login successful, token obtained")
    
    # Step 4: Setup 2FA using the API
    print("\n🔐 Step 4: Setup 2FA via API")
    status, response = make_request('POST', '/2fa/setup', {}, headers=headers)
    
    if status != 200:
        print(f"❌ 2FA setup failed with status {status}")
        return False
    
    qr_code = response.get('qr_code')
    secret = response.get('secret')
    recovery_codes = response.get('backupCodes', [])  # API returns 'backupCodes'
    
    print(f"✅ 2FA setup initiated")
    print(f"   Secret: {secret}")
    print(f"   Recovery codes: {len(recovery_codes)} codes generated")
    
    # Step 5: Enable 2FA by confirming with a test token
    print("\n� Step 5: Confirm 2FA setup (manually enable)")
    try:
        from app.models.User import User
        user_obj = User.where('email', EMAIL).first()
        if user_obj:
            user_obj.two_factor_enabled = True
            user_obj.two_factor_secret = secret
            user_obj.backup_codes_remaining = len(recovery_codes)
            # Store the recovery codes
            for i, code in enumerate(recovery_codes[:3], 1):
                setattr(user_obj, f'backup_code_{i}', code)
            user_obj.save()
            print(f"✅ 2FA manually confirmed and enabled with {len(recovery_codes)} recovery codes")
    except Exception as e:
        print(f"❌ Failed to confirm 2FA: {e}")
        return False
    
    # Step 6: Verify we have recovery codes before disable
    print("\n🔍 Step 6: Check state before disable")
    user = check_user_2fa_status()
    
    backup_codes_before = getattr(user, 'backup_codes_remaining', 0)
    print(f"📊 Before disable: {backup_codes_before} backup codes remaining")
    
    if backup_codes_before == 0:
        print("❌ No backup codes found after setup - test setup failed")
        return False
      # Step 7: Disable 2FA using the API
    print("\n🔒 Step 7: Disable 2FA via API")
    status, response = make_request('POST', '/2fa/disable', {
        'token': '000000',  # Use dummy token
        'password': PASSWORD  # Provide password as backup verification
    }, headers=headers)
    
    if status == 200:
        print("✅ 2FA disabled successfully via API")
    else:
        print(f"❌ 2FA disable failed: {response}")
        return False    
    # Step 8: Check state after disable
    print("\n🔍 Step 8: Check state after disable")
    user = check_user_2fa_status()
    
    backup_codes_after = getattr(user, 'backup_codes_remaining', 0)
    print(f"📊 After disable: {backup_codes_after} backup codes remaining")
    
    # Step 9: Verify cleanup
    print("\n🎯 Step 9: Verify cleanup results")
    success = True
    
    if user.two_factor_enabled:
        print("❌ FAIL: 2FA is still enabled")
        success = False
    else:
        print("✅ PASS: 2FA is disabled")
    
    if user.two_factor_secret:
        print("❌ FAIL: 2FA secret still exists")
        success = False
    else:
        print("✅ PASS: 2FA secret cleared")
    
    if backup_codes_after > 0:
        print(f"❌ FAIL: {backup_codes_after} backup codes still remaining")
        success = False
    else:
        print("✅ PASS: All backup codes cleared")
    
    if getattr(user, 'backup_code_1', None):
        print("❌ FAIL: backup_code_1 still exists")
        success = False
    else:
        print("✅ PASS: backup_code_1 cleared")
        
    if getattr(user, 'backup_code_2', None):
        print("❌ FAIL: backup_code_2 still exists")
        success = False
    else:
        print("✅ PASS: backup_code_2 cleared")
        
    if getattr(user, 'backup_code_3', None):
        print("❌ FAIL: backup_code_3 still exists")
        success = False
    else:
        print("✅ PASS: backup_code_3 cleared")
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Recovery codes are properly cleaned up when 2FA is disabled")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Recovery codes cleanup may not be working correctly")
    
    return success

if __name__ == "__main__":
    test_2fa_disable_cleanup()
