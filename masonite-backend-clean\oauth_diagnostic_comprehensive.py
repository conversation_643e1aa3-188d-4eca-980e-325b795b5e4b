#!/usr/bin/env python3
"""OAuth diagnostic script to test and fix database issues."""

import os
import sys
import json
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up Django settings equivalent for Masonite
os.environ.setdefault('APP_ENV', 'development')

try:
    from app.models.User import User
    from app.services.OAuthService import OAuthService
    from masoniteorm.query import QueryBuilder
    from masoniteorm.connections import ConnectionResolver
    from config.database import DATABASES
    
    print("✅ Successfully imported required modules")
    
    def test_database_connection():
        """Test database connection and oauth_providers field."""
        print("\n🔍 Testing database connection and schema...")
        
        try:
            # Test basic connection
            user_count = User.count()
            print(f"✅ Database connected. Total users: {user_count}")            # Test oauth_providers field handling
            test_user_data = {
                'name': 'Test OAuth User',  # Add required name field
                'email': '<EMAIL>',
                'password': 'dummy_oauth_password',  # Add dummy password for OAuth users
                'first_name': 'Test',
                'last_name': 'OAuth',
                'oauth_providers': json.dumps(['google']),  # Use JSON string directly
                'roles': 'user'
            }
            
            # Try to create a test user
            print("\n🧪 Testing oauth_providers JSON field...")
            
            # Clean up any existing test user
            existing_test_user = User.where('email', test_user_data['email']).first()
            if existing_test_user:
                existing_test_user.delete()
                print("🧹 Cleaned up existing test user")
            
            # Create test user
            test_user = User.create(test_user_data)
            print(f"✅ Created test user with oauth_providers: {test_user.oauth_providers}")
              # Test updating oauth_providers
            User.where('id', test_user.id).update({
                'oauth_providers': json.dumps(['google', 'github'])
            })
            test_user = User.where('id', test_user.id).first()  # Refresh
            print(f"✅ Updated oauth_providers: {test_user.oauth_providers}")
            
            # Test retrieval
            retrieved_user = User.where('email', test_user_data['email']).first()
            print(f"✅ Retrieved oauth_providers: {retrieved_user.oauth_providers}")
            print(f"   Type: {type(retrieved_user.oauth_providers)}")
            
            # Clean up
            test_user.delete()
            print("🧹 Cleaned up test user")
            
            return True
            
        except Exception as e:
            print(f"❌ Database test failed: {str(e)}")
            return False
    
    def test_oauth_service():
        """Test OAuth service configuration."""
        print("\n🔍 Testing OAuth service configuration...")
        
        try:
            oauth_service = OAuthService()
            
            # Test provider configurations
            providers = ['google', 'github', 'microsoft']
            for provider in providers:
                config = oauth_service.get_provider_config(provider)
                is_configured = oauth_service.is_provider_configured(provider)
                
                print(f"\n{provider.title()} OAuth:")
                print(f"  Client ID: {'✅ Set' if config and config.get('client_id') else '❌ Missing'}")
                print(f"  Client Secret: {'✅ Set' if config and config.get('client_secret') else '❌ Missing'}")
                print(f"  Configured: {'✅ Yes' if is_configured else '❌ No'}")
                
                if config:
                    print(f"  Auth URL: {config.get('auth_url')}")
                    print(f"  Token URL: {config.get('token_url')}")
                    print(f"  Scope: {config.get('scope')}")
            
            # Test redirect URL
            print(f"\nOAuth Redirect URL: {oauth_service.oauth_redirect_url}")
            
            return True
            
        except Exception as e:
            print(f"❌ OAuth service test failed: {str(e)}")
            return False
    
    def check_environment_variables():
        """Check OAuth environment variables."""
        print("\n🔍 Checking OAuth environment variables...")
        
        required_vars = [
            'GOOGLE_CLIENT_ID',
            'GOOGLE_CLIENT_SECRET',
            'GITHUB_CLIENT_ID', 
            'GITHUB_CLIENT_SECRET',
            'MICROSOFT_CLIENT_ID',
            'MICROSOFT_CLIENT_SECRET',
            'OAUTH_REDIRECT_URL'
        ]
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                print(f"✅ {var}: Set (length: {len(value)})")
            else:
                print(f"❌ {var}: Missing")
    
    def diagnose_oauth_errors():
        """Diagnose common OAuth errors."""
        print("\n🔍 Diagnosing common OAuth issues...")
        
        print("\n📋 Common OAuth Error Solutions:")
        print("1. Database Type Error (oauth_providers):")
        print("   - Ensure __casts__ includes 'oauth_providers': 'json' in User model")
        print("   - Use list assignment instead of array functions")
        
        print("\n2. Google 'Token used too early' Error:")
        print("   - Check system clock synchronization")
        print("   - Add clock skew tolerance in token verification")
        
        print("\n3. GitHub 401 Unauthorized Error:")
        print("   - Verify GitHub client secret in .env")
        print("   - Check GitHub app authorization callback URL")
        print("   - Ensure scope includes 'user:email'")
        
        print("\n4. Microsoft 403 Forbidden Error:")
        print("   - Verify Microsoft app permissions include 'User.Read'")
        print("   - Check Azure app registration redirect URI")
        print("   - Ensure proper tenant configuration")
        
        print("\n5. Redirect URL Mismatch:")
        print("   - Current setting: " + os.getenv('OAUTH_REDIRECT_URL', 'Not set'))
        print("   - Should match registered OAuth app callback URLs")
    
    def main():
        """Run all OAuth diagnostics."""
        print("🚀 OAuth Diagnostic Tool")
        print("=" * 50)
        
        # Check environment variables
        check_environment_variables()
        
        # Test database
        db_ok = test_database_connection()
        
        # Test OAuth service
        oauth_ok = test_oauth_service()
        
        # Show diagnostics
        diagnose_oauth_errors()
        
        print("\n" + "=" * 50)
        print("📊 Diagnostic Summary:")
        print(f"Database: {'✅ OK' if db_ok else '❌ Issues found'}")
        print(f"OAuth Service: {'✅ OK' if oauth_ok else '❌ Issues found'}")
        
        if db_ok and oauth_ok:
            print("\n🎉 OAuth system appears to be configured correctly!")
            print("If you're still seeing errors, check the specific provider configurations.")
        else:
            print("\n⚠️  Issues found. Please fix the above problems and try again.")
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure you're running this from the Masonite project root with the virtual environment activated.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
