#!/usr/bin/env python3

import requests
import time
import sys
from urllib.parse import quote

def test_oauth_redirect_directly():
    """Test OAuth redirect directly by simulating the callback"""
    print("🔗 Direct OAuth Redirect Test")
    print("=" * 50)
    
    backend_url = "http://localhost:3002"
      # Test OAuth callback directly (simulate OAuth provider callback)
    print("🔄 Testing OAuth callback redirect...")
    try:
        callback_url = f"{backend_url}/api/auth/oauth/callback"
        params = {
            'code': 'test_auth_code_12345',
            'state': 'google_1640000000_abcd1234'
        }
        
        print(f"Making request to: {callback_url}")
        print(f"With parameters: {params}")
        
        # Use allow_redirects=False to capture the redirect
        response = requests.get(callback_url, params=params, allow_redirects=False, timeout=10)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code in [301, 302, 303, 307, 308]:
            redirect_url = response.headers.get('Location', 'No Location header')
            print(f"✅ Redirect detected!")
            print(f"🔗 Redirect URL: {redirect_url}")
            
            if "localhost:4200" in redirect_url:
                print("✅ Redirect correctly points to frontend port 4200!")
                return True
            elif "localhost:" in redirect_url:
                print(f"❌ Redirect points to wrong port: {redirect_url}")
                return False
            else:
                print(f"❌ Redirect URL format unexpected: {redirect_url}")
                return False
        else:
            print(f"❌ No redirect detected. Response body: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OAuth callback: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_endpoints():
    """Test simple endpoints to verify server is working"""
    print("\n🌐 Testing Basic Endpoints")
    print("=" * 40)
    
    backend_url = "http://localhost:3002"
    
    # Test root
    try:
        response = requests.get(f"{backend_url}/", timeout=5)
        print(f"Root endpoint: {response.status_code}")
    except Exception as e:
        print(f"Root endpoint error: {e}")
    
    # Test API root
    try:
        response = requests.get(f"{backend_url}/api", timeout=5)
        print(f"API root: {response.status_code}")
    except Exception as e:
        print(f"API root error: {e}")

if __name__ == "__main__":
    print("🧪 OAuth Direct Redirect Test")
    print("=" * 60)
    
    test_simple_endpoints()
    success = test_oauth_redirect_directly()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ OAuth redirect test passed! Port 4200 redirect is working.")
    else:
        print("❌ OAuth redirect test failed. Check the redirect URL configuration.")
