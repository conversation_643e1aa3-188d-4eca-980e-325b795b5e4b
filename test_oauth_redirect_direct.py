#!/usr/bin/env python3

import requests
import time

def test_oauth_redirect_direct():
    """Test OAuth redirect directly"""
    print("🔗 Direct OAuth Redirect Test")
    print("=" * 50)
    
    backend_url = "http://localhost:3002"
    
    # Test OAuth callback simulation (simulate auth with error)
    print("🔄 Testing OAuth callback redirect...")
    try:
        # Simulate a callback with error to trigger redirect
        callback_url = f"{backend_url}/api/auth/oauth/callback"
        params = {
            'error': 'access_denied',
            'error_description': 'User denied access'
        }
        
        # Use allow_redirects=False to see where it would redirect
        response = requests.get(callback_url, params=params, allow_redirects=False, timeout=10)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code in [302, 301]:  # Redirect status codes
            redirect_url = response.headers.get('Location', '')
            print(f"✅ OAuth callback redirected to: {redirect_url}")
            
            if "localhost:4200" in redirect_url:
                print("✅ SUCCESS: Redirect URL correctly points to frontend with port 4200!")
                return True
            elif "localhost" in redirect_url and "4200" not in redirect_url:
                print("❌ ISSUE: Redirect URL points to localhost but wrong port")
                print(f"Expected: http://localhost:4200/auth/oauth-error")
                print(f"Actual: {redirect_url}")
                return False
            else:
                print("❌ ISSUE: Unexpected redirect URL format")
                return False
        else:
            print(f"❌ OAuth callback did not redirect. Status: {response.status_code}")
            if response.text:
                print(f"Response body: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OAuth callback: {e}")
        return False

if __name__ == "__main__":
    print("🧪 OAuth Redirect Direct Test")
    print("=" * 60)
    
    success = test_oauth_redirect_direct()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ OAuth redirect test PASSED! Frontend URL is correctly set to port 4200.")
        print("🎉 The OAuth flow should now work properly with the Angular frontend.")
    else:
        print("❌ OAuth redirect test FAILED. The frontend URL is still incorrect.")
        print("💡 Check the OAuthController frontend_url configuration.")
