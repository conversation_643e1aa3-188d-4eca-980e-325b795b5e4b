"""Test script to verify OAuthController env loading with Masonite's env function"""

import os
import sys

# Add the masonite-backend-clean directory to the path
sys.path.insert(0, r'c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean')

# Set up environment similar to Masonite
from masonite.environment import LoadEnvironment

def test_oauth_controller_with_masonite_env():
    print("🔍 Testing OAuthController with Masonite env function...")
    
    try:
        # Load environment first
        LoadEnvironment()
        
        # Import and test the controller
        from app.controllers.OAuthController import OAuthController
        
        controller = OAuthController()
        print(f"✅ OAuthController frontend_url: {controller.frontend_url}")
        
        # Test redirect URL construction
        success_url = f"{controller.frontend_url}/auth/oauth-success?code=test123&provider=google"
        error_url = f"{controller.frontend_url}/auth/oauth-error?error=test_error"
        
        print(f"✅ Success URL: {success_url}")
        print(f"✅ Error URL: {error_url}")
        
        # Verify the port is correct
        if ":4200" in controller.frontend_url:
            print("✅ Correct port 4200 detected!")
        else:
            print("❌ Port 4200 NOT detected!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_oauth_controller_with_masonite_env()
