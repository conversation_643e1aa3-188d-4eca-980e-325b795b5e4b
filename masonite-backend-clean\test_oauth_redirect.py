#!/usr/bin/env python3
"""
OAuth Redirect Testing Script
Tests the complete OAuth redirect flow to identify where the issue is occurring
"""

import requests
import sys
import os
from urllib.parse import urlparse, parse_qs

def test_frontend_accessibility():
    """Test if the frontend is accessible"""
    print("🔍 Testing Frontend Accessibility...")
    
    frontend_urls = [
        "http://localhost:4200",
        "http://localhost:4200/auth/oauth-success",
        "http://localhost:4200/auth/oauth-error"
    ]
    
    for url in frontend_urls:
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {url} - Status: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - Connection Error (Frontend not running?)")
        except Exception as e:
            print(f"⚠️ {url} - Error: {str(e)}")

def test_backend_oauth_endpoints():
    """Test backend OAuth endpoints"""
    print("\n🔍 Testing Backend OAuth Endpoints...")
    
    backend_base = "http://localhost:3002"
    
    # Test OAuth URL generation
    try:
        response = requests.get(f"{backend_base}/api/oauth/google/url", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OAuth URL Generation: {response.status_code}")
            print(f"   URL: {data.get('url', 'Not found')[:100]}...")
            return data.get('url')
        else:
            print(f"❌ OAuth URL Generation: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ OAuth URL Generation Error: {str(e)}")
        return None

def test_oauth_callback():
    """Test OAuth callback endpoint"""
    print("\n🔍 Testing OAuth Callback...")
    
    backend_base = "http://localhost:3002"
    
    # Test callback with error parameter (should redirect to frontend error page)
    try:
        response = requests.get(
            f"{backend_base}/api/oauth/callback?error=test_error&state=google_123456_abcdef",
            allow_redirects=False,
            timeout=5
        )
        
        if response.status_code in [302, 301]:
            redirect_url = response.headers.get('Location', 'No location header')
            print(f"✅ OAuth Callback (Error Test): {response.status_code}")
            print(f"   Redirect URL: {redirect_url}")
            
            # Parse the redirect URL
            parsed = urlparse(redirect_url)
            if parsed.netloc == 'localhost:4200':
                print("   ✅ Redirects to correct frontend URL")
            else:
                print(f"   ❌ Unexpected redirect host: {parsed.netloc}")
        else:
            print(f"❌ OAuth Callback: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ OAuth Callback Error: {str(e)}")

def test_specific_redirect_url():
    """Test specific redirect URL that might be failing"""
    print("\n🔍 Testing Specific Redirect Scenarios...")
    
    # Test URLs that should work
    test_urls = [
        "http://localhost:4200/auth/oauth-success?code=test123&provider=google",
        "http://localhost:4200/auth/oauth-error?error=test_error"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {url}")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   Frontend page loads successfully")
            else:
                print(f"   Frontend returned: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url}")
            print("   Connection Error - Frontend not accessible")
        except Exception as e:
            print(f"⚠️ {url}")
            print(f"   Error: {str(e)}")

def main():
    print("🚀 OAuth Redirect Diagnostic Test")
    print("=" * 50)
    
    # Check environment
    cors_origin = os.getenv('CORS_ORIGIN', 'Not set')
    frontend_url = os.getenv('FRONTEND_URL', 'Not set')
    
    print(f"Environment Check:")
    print(f"  CORS_ORIGIN: {cors_origin}")
    print(f"  FRONTEND_URL: {frontend_url}")
    print()
    
    # Run tests
    test_frontend_accessibility()
    oauth_url = test_backend_oauth_endpoints()
    test_oauth_callback()
    test_specific_redirect_url()
    
    print("\n" + "=" * 50)
    print("🎯 Diagnostic Summary:")
    print("1. Check if frontend (Angular) is running on http://localhost:4200")
    print("2. Check if backend (Masonite) is running on http://localhost:3002")
    print("3. Check if OAuth redirect URLs are properly configured")
    print("4. Verify that frontend can handle /auth/oauth-success route")
    
    if oauth_url:
        print(f"\n🔗 To test complete flow, visit:")
        print(f"   {oauth_url}")

if __name__ == "__main__":
    main()
