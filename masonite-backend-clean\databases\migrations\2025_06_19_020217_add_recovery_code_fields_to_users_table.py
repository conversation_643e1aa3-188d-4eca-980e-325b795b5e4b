"""AddRecoveryCodeFieldsToUsersTable Migration."""

from masoniteorm.migrations import Migration


class AddRecoveryCodeFieldsToUsersTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.table("users") as table:
            # Individual recovery code fields (hashed) - LoopBack compatible
            table.string("backup_code_1").nullable()
            table.string("backup_code_2").nullable()
            table.string("backup_code_3").nullable()
            table.integer("backup_codes_remaining").default(0)
            table.timestamp("backup_codes_generated_at").nullable()

    def down(self):
        """
        Revert the migrations.
        """
        with self.schema.table("users") as table:
            table.drop_column("backup_code_1")
            table.drop_column("backup_code_2")
            table.drop_column("backup_code_3")
            table.drop_column("backup_codes_remaining")
            table.drop_column("backup_codes_generated_at")
