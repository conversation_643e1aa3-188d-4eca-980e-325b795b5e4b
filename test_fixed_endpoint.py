#!/usr/bin/env python3
"""
Test the fixed /api/auth/request-disable-2fa endpoint
"""

import requests
import json

def test_disable_2fa_endpoint():
    """Test the disable 2FA endpoint with various scenarios"""
    
    base_url = "http://localhost:3002"
    endpoint = f"{base_url}/api/auth/request-disable-2fa"
    
    print("🧪 Testing /api/auth/request-disable-2fa endpoint...")
    print(f"📍 URL: {endpoint}")
    
    # Test scenarios
    test_cases = [
        {
            "name": "Valid email with reason",
            "data": {
                "email": "<EMAIL>",
                "reason": "recovery_codes_exhausted"
            }
        },
        {
            "name": "Valid email without reason",
            "data": {
                "email": "<EMAIL>"
            }
        },
        {
            "name": "Invalid email format",
            "data": {
                "email": "invalid-email",
                "reason": "lost_device"
            }
        },
        {
            "name": "Missing email",
            "data": {
                "reason": "other"
            }
        },
        {
            "name": "Invalid reason",
            "data": {
                "email": "<EMAIL>",
                "reason": "invalid_reason"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print(f"📤 Request data: {json.dumps(test_case['data'], indent=2)}")
        
        try:
            response = requests.post(
                endpoint,
                json=test_case['data'],
                headers={
                    'Content-Type': 'application/json',
                    'Origin': 'http://localhost:3000'
                },
                timeout=10
            )
            
            print(f"📥 Status: {response.status_code}")
            print(f"📥 Headers: {dict(response.headers)}")
            
            try:
                response_data = response.json()
                print(f"📥 Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"📥 Response (text): {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_disable_2fa_endpoint()
