#!/usr/bin/env python3

import requests
import time
import sys
from urllib.parse import urlparse, parse_qs

def test_oauth_endpoint_live():
    """Test OAuth endpoints with the live backend server"""
    print("🚀 OAuth Live Endpoint Test")
    print("=" * 50)
    
    backend_url = "http://localhost:3002"
    
    # Test if backend is running
    try:
        response = requests.get(f"{backend_url}/", timeout=5)
        print(f"✅ Backend is running: {response.status_code}")
    except requests.exceptions.RequestException:
        print("❌ Backend is not running. Please start the backend first.")
        return False
    
    # Test OAuth providers endpoint
    try:
        response = requests.get(f"{backend_url}/api/oauth/providers", timeout=5)
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ OAuth providers endpoint: {providers}")
        else:
            print(f"❌ OAuth providers endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing OAuth providers: {e}")
        return False
    
    # Test OAuth URL generation for Google
    try:
        response = requests.get(f"{backend_url}/api/oauth/google/url", timeout=5)
        if response.status_code == 200:
            oauth_data = response.json()
            oauth_url = oauth_data.get('url', '')
            print(f"✅ Google OAuth URL generated successfully")
            print(f"🔗 OAuth URL: {oauth_url[:100]}...")
            
            # Test redirect URL in the OAuth URL (should point to backend callback)
            if "localhost:3002" in oauth_url and "oauth/callback" in oauth_url:
                print("✅ OAuth URL contains correct backend callback URL")
            else:
                print("❌ OAuth URL callback URL may be incorrect")
                
        else:
            print(f"❌ Google OAuth URL generation failed: {response.status_code}")
            if response.text:
                print(f"Error response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing Google OAuth URL: {e}")
        return False
    
    # Test OAuth callback simulation (simulate successful auth)
    print("\n🔄 Testing OAuth callback simulation...")
    try:
        # Simulate a callback with minimal required parameters
        callback_url = f"{backend_url}/api/oauth/callback"
        params = {
            'code': 'test_auth_code_12345',
            'state': 'google_test_state_12345'
        }
        
        # Use allow_redirects=False to see where it would redirect
        response = requests.get(callback_url, params=params, allow_redirects=False, timeout=10)
        
        if response.status_code in [302, 301]:  # Redirect status codes
            redirect_url = response.headers.get('Location', '')
            print(f"✅ OAuth callback redirected to: {redirect_url}")
            
            if "localhost:4200" in redirect_url and "/auth/oauth-" in redirect_url:
                print("✅ Redirect URL correctly points to frontend with port 4200!")
                return True
            else:
                print("❌ Redirect URL does not point to correct frontend")
                return False
        else:
            print(f"❌ OAuth callback did not redirect. Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OAuth callback: {e}")
        return False

if __name__ == "__main__":
    print("🧪 OAuth Live Backend Test")
    print("=" * 60)
    
    success = test_oauth_endpoint_live()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ OAuth live test passed! Frontend URL redirection is working correctly.")
        print("🎉 The OAuth flow should now work properly with the Angular frontend.")
    else:
        print("❌ OAuth live test failed. Please check the backend configuration.")
        print("💡 Make sure the backend server is running on port 3002.")
