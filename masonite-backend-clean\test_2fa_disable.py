#!/usr/bin/env python3
"""
Test script to generate and test 2FA disable tokens
"""
import jwt
from datetime import datetime, timedelta
import os
import sys
import requests
import json

# Set up environment
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def generate_test_token(user_id=278, email="<EMAIL>", reason="recovery_codes_exhausted"):
    """Generate a fresh test token"""
    import time
    # Use the same fallback secret as the server (based on the debug log)
    secret_key = "ZAq1N9Mw2BRTOyCQRew0F9pyUafCRNx4IbvDKAUc7LH7JkGYRD/pPOyYuNO1Q5po"
    
    # Use time.time() for consistency with JWT standards
    current_time = time.time()
    expiry_time = current_time + (60 * 60)  # 1 hour from now
    
    token_payload = {
        'user_id': user_id,
        'email': email,
        'action': 'disable_2fa',
        'reason': reason,
        'exp': expiry_time  # 1 hour expiry
    }
    
    print(f"Using secret key: {secret_key[:20]}...")
    print(f"Current timestamp: {current_time}")
    print(f"Expiry timestamp: {expiry_time}")
    print(f"Current time: {datetime.fromtimestamp(current_time)}")
    print(f"Expiry time: {datetime.fromtimestamp(expiry_time)}")
    
    token = jwt.encode(token_payload, secret_key, algorithm='HS256')
    return token

def decode_token(token):
    """Decode and display token contents"""
    import time
    secret_key = "ZAq1N9Mw2BRTOyCQRew0F9pyUafCRNx4IbvDKAUc7LH7JkGYRD/pPOyYuNO1Q5po"  # Same fallback as server
    
    try:
        # First decode without verification to see the payload
        unverified_payload = jwt.decode(token, options={"verify_signature": False})
        print(f"Unverified token payload: {json.dumps(unverified_payload, indent=2)}")
        
        # Now try verified decode
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        print(f"Verified token payload: {json.dumps(payload, indent=2)}")
        
        # Check expiry using time.time() for consistency
        current_timestamp = time.time()
        exp_time = datetime.fromtimestamp(payload['exp'])
        current_time = datetime.fromtimestamp(current_timestamp)
        
        print(f"Token expiry timestamp: {payload['exp']}")
        print(f"Current timestamp: {current_timestamp}")
        print(f"Token expires at: {exp_time}")
        print(f"Current time: {current_time}")
        print(f"Time difference: {payload['exp'] - current_timestamp} seconds")
        print(f"Valid: {'Yes' if current_timestamp < payload['exp'] else 'No (EXPIRED)'}")
        
        return payload
    except jwt.ExpiredSignatureError:
        print("❌ Token has expired")
        return None
    except jwt.InvalidTokenError as e:
        print(f"❌ Invalid token: {e}")
        return None

def test_confirm_endpoint(token):
    """Test the confirm-disable-2fa endpoint"""
    url = "http://localhost:3002/api/auth/confirm-disable-2fa"
    headers = {"Content-Type": "application/json"}
    data = {"token": token}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"\n🔗 Testing confirm endpoint:")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return None

if __name__ == "__main__":
    print("🔐 2FA Disable Token Test")
    print("=" * 50)
    
    # Generate fresh token
    print("\n1. Generating fresh token...")
    fresh_token = generate_test_token()
    print(f"Fresh token: {fresh_token}")
    
    # Decode and validate token
    print("\n2. Decoding token...")
    payload = decode_token(fresh_token)
    
    if payload:
        # Test the endpoint
        print("\n3. Testing confirm endpoint...")
        response = test_confirm_endpoint(fresh_token)
        
        if response and response.status_code == 200:
            print("✅ 2FA disable confirmed successfully!")
        else:
            print(f"❌ Failed to confirm 2FA disable")
    
    print("\n" + "=" * 50)
