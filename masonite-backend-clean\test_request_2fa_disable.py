"""
Test 2FA disable request to see the actual email URL generated
"""

import requests
import json

# Test the request-disable-2fa endpoint
url = "http://localhost:8001/api/auth/request-disable-2fa"

# Request data
data = {
    "email": "<EMAIL>",
    "reason": "lost_device"
}

# Headers
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoyNzgsImV4cCI6MTc1MDMzNzIwN30.GlSEqsBfEHIc6R1tBNvRztKsHhbG_nnWK_M6CX-lKTI"  # Use a valid auth token
}

print("🔗 Testing request-disable-2fa endpoint:")
print(f"URL: {url}")
print(f"Data: {json.dumps(data, indent=2)}")

try:
    response = requests.post(url, json=data, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ 2FA disable request successful!")
        print("📧 Check your email for the disable confirmation link")
    else:
        print(f"❌ Request failed")
        
except Exception as e:
    print(f"❌ Error: {e}")
