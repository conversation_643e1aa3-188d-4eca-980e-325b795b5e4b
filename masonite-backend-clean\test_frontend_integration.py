#!/usr/bin/env python3
"""
Test Frontend Auth Integration - JWT Token Validation
Tests that our JWT tokens work with the /api/auth/verify-token endpoint
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:3002"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "Test123!"

def test_frontend_auth_flow():
    """Test the complete frontend authentication flow"""
    print("🧪 Testing Frontend Authentication Integration...")
    
    # Step 1: Login without 2FA (should require 2FA)
    print("\n1️⃣ Testing login without 2FA...")
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD
        },
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200 or not response.json().get('requiresTwoFactor'):
        print("❌ Expected requiresTwoFactor=true")
        return False
    
    print("✅ Correctly requires 2FA")
    
    # Step 2: Complete 2FA login and get token
    print("\n2️⃣ Testing 2FA login to get JWT token...")
    
    # Import to get current TOTP token
    import pyotp
    from app.models.User import User
    
    user = User.where('email', TEST_EMAIL).first()
    if not user or not user.two_factor_secret:
        print("❌ Test user not found or 2FA not setup")
        return False
    
    totp = pyotp.TOTP(user.two_factor_secret)
    current_token = totp.now()
    
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD,
            'twoFactorToken': current_token
        },
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print(f"❌ 2FA login failed: {response.status_code}")
        print(response.text)
        return False
    
    data = response.json()
    jwt_token = data.get('token')
    
    if not jwt_token:
        print("❌ No JWT token received")
        return False
    
    print(f"✅ Received JWT token: {jwt_token[:50]}...")
    
    # Step 3: Validate token with verify-token endpoint
    print("\n3️⃣ Testing token validation with /api/auth/verify-token...")
    
    response = requests.post(f"{BASE_URL}/api/auth/verify-token", 
        headers={
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {jwt_token}'
        }
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        token_data = response.json()
        if token_data.get('valid') and token_data.get('user'):
            print("✅ Token validation successful")
            return True
        else:
            print("❌ Token validation response missing valid/user fields")
            return False
    else:
        print("❌ Token validation failed")
        return False

def test_auth_guard_compatibility():
    """Test that our response matches what the Angular auth guard expects"""
    print("\n🧪 Testing Auth Guard Compatibility...")
    
    # Get a valid token first
    import pyotp
    from app.models.User import User
    
    user = User.where('email', TEST_EMAIL).first()
    totp = pyotp.TOTP(user.two_factor_secret)
    current_token = totp.now()
    
    # Login with 2FA
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD,
            'twoFactorToken': current_token
        },
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code != 200:
        print("❌ Could not get valid token")
        return False
    
    jwt_token = response.json().get('token')
    
    # Test the /auth/me endpoint that the frontend uses
    print("Testing /api/auth/me endpoint...")
    response = requests.get(f"{BASE_URL}/api/auth/me", 
        headers={
            'Authorization': f'Bearer {jwt_token}'
        }
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        print("✅ /auth/me endpoint works")
        return True
    else:
        print(f"❌ /auth/me failed: {response.text}")
        return False

def main():
    """Run frontend integration tests"""
    print("🚀 Starting Frontend Authentication Integration Tests...")
    
    results = []
    
    # Test 1: Complete auth flow
    results.append(test_frontend_auth_flow())
    
    # Test 2: Auth guard compatibility  
    results.append(test_auth_guard_compatibility())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Frontend Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Frontend authentication integration is working correctly!")
        print("✅ The Angular frontend should now be able to:")
        print("   - Handle 2FA login flow properly")
        print("   - Store JWT tokens correctly") 
        print("   - Validate authentication state")
        print("   - Navigate to protected routes")
    else:
        print("❌ Some frontend integration issues remain")
    
    return passed == total

if __name__ == '__main__':
    main()
