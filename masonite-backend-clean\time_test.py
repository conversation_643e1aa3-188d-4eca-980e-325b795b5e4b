#!/usr/bin/env python3
"""
Quick test to check current time and timestamps
"""
from datetime import datetime
import time

print("Current system time checks:")
print("=" * 40)

# Check different time methods
now_utc = datetime.utcnow()
now_local = datetime.now()
timestamp_time = time.time()
timestamp_datetime = now_utc.timestamp()

print(f"datetime.utcnow(): {now_utc}")
print(f"datetime.now(): {now_local}")
print(f"time.time(): {timestamp_time}")
print(f"datetime.utcnow().timestamp(): {timestamp_datetime}")

# Check which one is close to the expected 2024 timestamp
print(f"\nTimestamp comparison:")
print(f"time.time() is approximately: {datetime.fromtimestamp(timestamp_time)}")
print(f"datetime.utcnow().timestamp() is approximately: {datetime.fromtimestamp(timestamp_datetime)}")

# Test with a known good timestamp (current time in 2024)
test_timestamp = 1703000000  # December 2023
print(f"\nTest timestamp {test_timestamp} is: {datetime.fromtimestamp(test_timestamp)}")
