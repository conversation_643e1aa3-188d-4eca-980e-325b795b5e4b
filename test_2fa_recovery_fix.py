#!/usr/bin/env python3
"""
Test 2FA Recovery Code Functionality
Tests both enabling 2FA (generating 3 codes) and using recovery codes during login
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:8001/api'
EMAIL = '<EMAIL>'
PASSWORD = 'SecurePass123!'

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request with error handling"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=headers)
        
        print(f"📊 {method.upper()} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"   Response: {json.dumps(response_data, indent=2)}")
            return response.status_code, response_data
        else:
            print(f"   Response: {response.text}")
            return response.status_code, {'message': response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return 0, {'error': str(e)}

def test_2fa_recovery_codes():
    """Test complete 2FA flow with recovery codes"""
    print("🧪 Testing 2FA Recovery Code Functionality")
    print("=" * 60)
    
    # Step 1: Login
    print("\n🔑 Step 1: Login to get access token")
    status, response = make_request('POST', '/auth/login', {
        'email': EMAIL,
        'password': PASSWORD
    })
      if status != 200:
        print(f"❌ Login failed with status {status}")
        return False
    
    token = response.get('token')  # Changed from user.apiToken to token
    if not token:
        print("❌ No API token received")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    print(f"✅ Login successful, token: {token[:20]}...")
    
    # Step 2: Check if 2FA is already enabled
    print("\n📋 Step 2: Check current 2FA status")
    status, response = make_request('GET', '/two-factor/status', headers=headers)
    
    if status == 200:
        current_status = response.get('enabled', False)
        print(f"🔍 Current 2FA status: {'Enabled' if current_status else 'Disabled'}")
        
        if current_status:
            print("\n🔄 Disabling existing 2FA first...")
            # Try to disable with password
            status, response = make_request('POST', '/two-factor/disable', {
                'password': PASSWORD
            }, headers=headers)
            
            if status == 200:
                print("✅ 2FA disabled successfully")
            else:
                print(f"⚠️ Failed to disable 2FA: {response}")
    
    # Step 3: Setup 2FA
    print("\n🔐 Step 3: Setup 2FA (should generate 3 recovery codes)")
    status, response = make_request('POST', '/two-factor/setup', headers=headers)
    
    if status != 200:
        print(f"❌ 2FA setup failed with status {status}")
        return False
    
    backup_codes = response.get('backupCodes', [])
    secret = response.get('secret')
    
    print(f"✅ 2FA setup successful!")
    print(f"🔑 Secret: {secret}")
    print(f"🎯 Backup codes count: {len(backup_codes)}")
    print(f"📝 Backup codes: {backup_codes}")
    
    if len(backup_codes) != 3:
        print(f"❌ ISSUE: Expected 3 backup codes, got {len(backup_codes)}")
        return False
    
    # Step 4: Generate and verify TOTP to enable 2FA
    print("\n🔐 Step 4: Enable 2FA (using TOTP verification)")
    
    # For testing, we'll use a static token (in real scenario, use TOTP)
    # This is a test implementation - in production you'd use the TOTP library
    import pyotp
    totp = pyotp.TOTP(secret)
    token_code = totp.now()
    
    status, response = make_request('POST', '/two-factor/verify', {
        'token': token_code
    }, headers=headers)
    
    if status != 200:
        print(f"⚠️ 2FA verification failed: {response}")
        # Try with a manual token input
        print("Please enter a 6-digit TOTP code from your authenticator:")
        manual_token = input("Enter TOTP code: ").strip()
        
        status, response = make_request('POST', '/two-factor/verify', {
            'token': manual_token
        }, headers=headers)
        
        if status != 200:
            print(f"❌ Manual verification also failed: {response}")
            return False
    
    print("✅ 2FA enabled successfully!")
    
    # Step 5: Test recovery codes during login
    print("\n🔄 Step 5: Test login with recovery codes")
    
    for i, recovery_code in enumerate(backup_codes):
        print(f"\n🧪 Testing recovery code {i+1}: {recovery_code}")
        
        # Attempt login with recovery code
        status, response = make_request('POST', '/auth/login', {
            'email': EMAIL,
            'password': PASSWORD,
            'recoveryCode': recovery_code
        })
        
        if status == 200:
            print(f"✅ Recovery code {i+1} login successful!")
            
            # Get new token
            new_token = response.get('user', {}).get('apiToken')
            if new_token:
                headers = {'Authorization': f'Bearer {new_token}'}
                print(f"🔑 New token: {new_token[:20]}...")
            
        else:
            print(f"❌ Recovery code {i+1} login failed: {response}")
            
            # Check if it's the "codes exhausted" error we're trying to fix
            error_message = response.get('error', {}).get('message', '')
            if 'exhausted' in error_message.lower() or 'remaining' in error_message.lower():
                print(f"🎯 FOUND THE ISSUE: {error_message}")
                break
    
    # Step 6: Try to use an already used recovery code
    print(f"\n🧪 Step 6: Test using already used recovery code")
    if backup_codes:
        status, response = make_request('POST', '/auth/login', {
            'email': EMAIL,
            'password': PASSWORD,
            'recoveryCode': backup_codes[0]  # Should be used already
        })
        
        if status != 200:
            print("✅ Correctly rejected already used recovery code")
            print(f"   Error: {response.get('error', {}).get('message', 'Unknown error')}")
        else:
            print("❌ ISSUE: Already used recovery code was accepted!")
    
    # Step 7: Check recovery codes status
    print("\n📊 Step 7: Check recovery codes status")
    status, response = make_request('GET', '/two-factor/recovery-codes', headers=headers)
    
    if status == 200:
        print("✅ Recovery codes status retrieved:")
        print(f"   Remaining codes: {response.get('remainingCodes', 'N/A')}")
        print(f"   Total codes: {response.get('totalCodes', 'N/A')}")
        print(f"   Status info: {response.get('recoveryCodes', [])}")
    else:
        print(f"❌ Failed to get recovery codes status: {response}")
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print(f"✅ 2FA setup: Generated {len(backup_codes)} codes (expected 3)")
    print("✅ Recovery code login testing completed")
    print("✅ Used code rejection testing completed")
    print("✅ Recovery codes status checking completed")
    
    return True

if __name__ == '__main__':
    try:
        success = test_2fa_recovery_codes()
        if success:
            print("\n🎉 All tests completed!")
        else:
            print("\n❌ Some tests failed!")
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
