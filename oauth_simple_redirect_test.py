#!/usr/bin/env python3

import requests
import time
from urllib.parse import quote

def test_oauth_redirect_simple():
    """Test OAuth redirect with minimal parameters to avoid OAuth service errors"""
    print("🔗 Simple OAuth Redirect Test")
    print("=" * 50)
    
    backend_url = "http://localhost:3002"
    
    # Test OAuth callback with invalid state to avoid OAuth service processing
    print("🔄 Testing OAuth callback redirect with invalid state...")
    try:
        callback_url = f"{backend_url}/api/auth/oauth/callback"
        params = {
            'error': 'access_denied',  # Simulate OAuth error to avoid token exchange
            'state': 'invalid_state'
        }
        
        print(f"Making request to: {callback_url}")
        print(f"With parameters: {params}")
        
        # Use allow_redirects=False to capture the redirect
        response = requests.get(callback_url, params=params, allow_redirects=False, timeout=10)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code in [301, 302, 303, 307, 308]:
            redirect_url = response.headers.get('Location', 'No Location header')
            print(f"✅ Redirect detected!")
            print(f"🔗 Redirect URL: {redirect_url}")
            
            if "localhost:4200" in redirect_url:
                print("✅ Redirect correctly points to frontend port 4200!")
                return True
            elif "localhost:" in redirect_url:
                print(f"❌ Redirect points to wrong port")
                # Extract port from URL
                if "localhost/" in redirect_url:
                    print("🔍 Redirect uses port 80 (default HTTP)")
                return False
            else:
                print(f"❌ Redirect URL format unexpected: {redirect_url}")
                return False
        else:
            print(f"❌ No redirect detected. Response body: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OAuth callback: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Simple OAuth Redirect Test")
    print("=" * 60)
    
    success = test_oauth_redirect_simple()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ OAuth redirect test passed! Port 4200 redirect is working.")
    else:
        print("❌ OAuth redirect test failed. Frontend URL issue confirmed.")
