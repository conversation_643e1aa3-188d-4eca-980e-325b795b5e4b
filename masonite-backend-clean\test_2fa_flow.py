#!/usr/bin/env python3
"""
Test 2FA login flow to debug token issues
"""

import requests
import json

def test_2fa_flow():
    print("🔍 Testing 2FA login flow...")
    
    # Step 1: Normal login (should return tempToken for 2FA users)
    login_data = {
        "email": "<EMAIL>",  # Use a user with 2FA enabled
        "password": "password123"
    }
    
    try:
        # Test login first
        login_response = requests.post(
            "http://localhost:3002/api/auth/login",
            json=login_data,
            headers={
                "Content-Type": "application/json",
                "Origin": "http://localhost:4200"
            },
            timeout=10
        )
        
        print(f"📊 Login Response Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"📊 Login Response: {json.dumps(login_result, indent=2)}")
            
            # Check if it requires 2FA
            if 'tempToken' in login_result and 'requiresTwoFactor' in login_result:
                print("✅ 2FA required - tempToken received")
                temp_token = login_result['tempToken']
                
                # Step 2: Verify 2FA (simulate with test code)
                verify_data = {
                    "tempToken": temp_token,
                    "token": "123456"  # Test code - will likely fail but shows response format
                }
                
                verify_response = requests.post(
                    "http://localhost:3002/api/auth/verify-2fa",
                    json=verify_data,
                    headers={
                        "Content-Type": "application/json",
                        "Origin": "http://localhost:4200"
                    },
                    timeout=10
                )
                
                print(f"📊 2FA Verify Response Status: {verify_response.status_code}")
                verify_result = verify_response.json()
                print(f"📊 2FA Verify Response: {json.dumps(verify_result, indent=2)}")
                
            else:
                print("ℹ️ No 2FA required - normal login token received")
        else:
            print(f"❌ Login failed: {login_response.json()}")
    
    except Exception as e:
        print(f"❌ Test error: {str(e)}")

if __name__ == "__main__":
    test_2fa_flow()
