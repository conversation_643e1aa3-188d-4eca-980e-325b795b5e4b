#!/usr/bin/env python3
"""
Direct test of login endpoint to debug the 500 error
"""

import requests
import json
from app.models.User import User

def test_login():
    # Get a test user from database
    user = User.first()
    if not user:
        print("❌ No users found in database!")
        return
    
    print(f"🔍 Testing login with user: {user.email}")
    
    # Test login data
    login_data = {
        "email": user.email,
        "password": "password123"  # Common test password
    }
    
    try:
        # Make login request
        response = requests.post(
            "http://localhost:3002/api/auth/login",
            json=login_data,
            headers={
                "Content-Type": "application/json",
                "Origin": "http://localhost:4200"
            },
            timeout=10
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            print(f"📊 Response Data: {response.json()}")
        else:
            print(f"❌ Login failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📊 Error Data: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📊 Raw Response: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused - server not running on port 3002")
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except Exception as e:
        print(f"❌ Request error: {str(e)}")

if __name__ == "__main__":
    test_login()
