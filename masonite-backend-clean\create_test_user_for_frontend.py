#!/usr/bin/env python3
"""
Create a test user with known credentials for frontend testing
"""

from app.models.User import User
from masonite.authentication import Auth
from datetime import datetime

def create_test_user():
    # Check if test user already exists
    test_email = "<EMAIL>"
    existing_user = User.where('email', test_email).first()
    
    if existing_user:
        print(f"✅ Test user already exists: {test_email}")
        existing_user.update({
            'password': Auth().hash('password123'),
            'email_verified_at': datetime.now()
        })
        print("✅ Updated password to 'password123' and verified email")
    else:
        # Create new test user
        user = User.create({
            'email': test_email,
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': Auth().hash('password123'),
            'email_verified_at': datetime.now()
        })
        print(f"✅ Created test user: {test_email} with password 'password123'")
    
    print("🔑 Frontend login credentials:")
    print(f"   Email: {test_email}")
    print(f"   Password: password123")

if __name__ == "__main__":
    create_test_user()
