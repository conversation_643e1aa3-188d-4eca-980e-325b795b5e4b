#!/usr/bin/env python3
"""
Test 2FA Login Flow - Complete LoopBack Compatible Test
Tests both TOTP verification and recovery code validation
"""

import requests
import json
import pyotp

# Configuration
BASE_URL = "http://localhost:3002"
TEST_EMAIL = "<EMAIL>"  # Use different email to avoid conflicts
TEST_PASSWORD = "Test123!"

def setup_test_user_with_2fa():
    """Set up a test user with 2FA enabled and recovery codes"""
    print("🔧 Setting up test user with 2FA...")
    
    # First, create the user through the database directly
    from app.models.User import User
    from app.services.RecoveryCodeService import RecoveryCodeService    # Create or get test user
    user = User.where('email', TEST_EMAIL).first()
    if user:
        print(f"📝 User already exists, updating: {user.email}")
        # Update the existing user
        user.set_password(TEST_PASSWORD)
        user.email_verified_at = '2024-01-01 00:00:00'
        user.is_active = True
        user.save()
    else:
        user = User()
        user.name = 'Test 2FA User'
        user.first_name = 'Test'
        user.last_name = 'User'
        user.email = TEST_EMAIL
        user.is_active = True
        user.roles = 'user'
        user.email_verified_at = '2024-01-01 00:00:00'
        user.set_password(TEST_PASSWORD)
        user.save()
        print(f'✅ Created user: {user.email} (ID: {user.id})')
    
    # Generate TOTP secret
    totp_secret = pyotp.random_base32()
    user.two_factor_enabled = True
    user.two_factor_secret = totp_secret
    user.save()
    
    print(f'🔐 TOTP Secret: {totp_secret}')
    
    # Generate recovery codes
    recovery_service = RecoveryCodeService()
    recovery_codes = recovery_service.generate_recovery_codes(user.id)
    
    print(f'🔑 Recovery codes: {recovery_codes}')
    
    # Generate current TOTP token for testing
    totp = pyotp.TOTP(totp_secret)
    current_token = totp.now()
    
    print(f'📱 Current TOTP token: {current_token}')
    
    return {
        'user_id': user.id,
        'email': TEST_EMAIL,
        'password': TEST_PASSWORD,
        'totp_secret': totp_secret,
        'recovery_codes': recovery_codes,
        'current_token': current_token
    }

def test_normal_login():
    """Test normal login without 2FA"""
    print("\n🧪 Testing normal login (should require 2FA)...")
    
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD
        },
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('requiresTwoFactor'):
            print("✅ Correctly requires 2FA")
            return True
        else:
            print("❌ Should require 2FA but didn't")
            return False
    else:
        print("❌ Login failed unexpectedly")
        return False

def test_2fa_with_totp(totp_token):
    """Test 2FA login with TOTP token"""
    print(f"\n🧪 Testing 2FA login with TOTP token: {totp_token}...")
    
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD,
            'twoFactorToken': totp_token
        },
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('token') and not data.get('requiresTwoFactor'):
            print("✅ 2FA login with TOTP successful")
            return True
        else:
            print("❌ 2FA login failed - no token or still requires 2FA")
            return False
    else:
        print("❌ 2FA login failed")
        return False

def test_2fa_with_invalid_totp():
    """Test 2FA login with invalid TOTP token"""
    print("\n🧪 Testing 2FA login with invalid TOTP token...")
    
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD,
            'twoFactorToken': '000000'  # Invalid token
        },
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 401:
        print("✅ Correctly rejected invalid TOTP token")
        return True
    else:
        print("❌ Should have rejected invalid TOTP token")
        return False

def test_2fa_with_recovery_code(recovery_code):
    """Test 2FA login with recovery code"""
    print(f"\n🧪 Testing 2FA login with recovery code: {recovery_code}...")
    
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD,
            'recoveryCode': recovery_code
        },
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('token') and not data.get('requiresTwoFactor'):
            print("✅ 2FA login with recovery code successful")
            return True
        else:
            print("❌ 2FA login failed - no token or still requires 2FA")
            return False
    else:
        print("❌ 2FA login with recovery code failed")
        return False

def test_2fa_with_invalid_recovery_code():
    """Test 2FA login with invalid recovery code"""
    print("\n🧪 Testing 2FA login with invalid recovery code...")
    
    response = requests.post(f"{BASE_URL}/api/auth/login", 
        json={
            'email': TEST_EMAIL,
            'password': TEST_PASSWORD,
            'recoveryCode': 'INVALID1'  # Invalid code
        },
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 401:
        print("✅ Correctly rejected invalid recovery code")
        return True
    else:
        print("❌ Should have rejected invalid recovery code")
        return False

def main():
    """Run all 2FA tests"""
    print("🚀 Starting 2FA Login Flow Tests...")
    
    # Setup
    test_data = setup_test_user_with_2fa()
    
    # Test cases
    results = []
    
    # 1. Normal login (should require 2FA)
    results.append(test_normal_login())
    
    # 2. Valid TOTP token
    results.append(test_2fa_with_totp(test_data['current_token']))
    
    # 3. Invalid TOTP token
    results.append(test_2fa_with_invalid_totp())
    
    # 4. Valid recovery code
    results.append(test_2fa_with_recovery_code(test_data['recovery_codes'][0]))
    
    # 5. Invalid recovery code
    results.append(test_2fa_with_invalid_recovery_code())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! 2FA flow is working correctly.")
    else:
        print("❌ Some tests failed. Check the implementation.")
    
    return passed == total

if __name__ == '__main__':
    main()
