"""
Masonite 4 Two-Factor Authentication Controller
Maintains exact LoopBack API compatibility for existing frontend
"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.mail import Mail
from masonite.notification import NotificationManager
from app.models.User import User
from app.controllers.CorsController import CorsController
import pyotp
import qrcode
import io
import base64
import secrets
import json
from datetime import datetime, timedelta


class TwoFactorController(Controller):
    """
    Two-Factor Authentication Controller 
    Maintains exact compatibility with LoopBack endpoints
    """
    
    def setup(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/two-factor/setup
        Initialize 2FA setup - generates secret and QR code
        """
        # Get authenticated user
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)
        
        # Check if user already has 2FA enabled
        if user.two_factor_enabled:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Two-factor authentication is already enabled. Please disable it first to set up again.'
                }
            }, 400)
        
        try:
            # Generate 2FA secret
            secret = pyotp.random_base32()
            
            # Create TOTP instance
            totp = pyotp.TOTP(secret)
            
            # Generate QR code URI
            qr_uri = totp.provisioning_uri(
                name=user.email,
                issuer_name='SecureApp'
            )
            
            # Generate QR code image
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(qr_uri)
            qr.make(fit=True)
            
            # Convert QR code to base64 image
            img = qr.make_image(fill_color="black", back_color="white")
            img_io = io.BytesIO()
            img.save(img_io, format='PNG')
            img_io.seek(0)
            qr_code_data = base64.b64encode(img_io.getvalue()).decode()
            qr_code = f"data:image/png;base64,{qr_code_data}"
              # Generate backup codes using RecoveryCodeService (3 codes like LoopBack)
            from app.services.RecoveryCodeService import RecoveryCodeService
            recovery_service = RecoveryCodeService()
            backup_codes = recovery_service.generate_recovery_codes(user.id)
            
            # Store secret temporarily (not enabled yet)
            user.two_factor_secret = secret
            user.save()

            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'secret': secret,
                'qrCode': qr_code,
                'backupCodes': backup_codes,
                'methods': ['authenticator', 'sms', 'email']
            })

        except Exception as e:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to setup 2FA. Please try again.'
                }
            }, 500)
    
    def verify(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/two-factor/verify
        Verify and enable 2FA with token
        """
        # Validate request
        errors = request.validate(
            validate.required(['token']),
            validate.string('token')
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        if not user.two_factor_secret:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Two-factor authentication not set up'
                }
            }, 400)
        
        try:
            token = request.input('token').replace(' ', '').strip()
            
            # Verify TOTP token
            totp = pyotp.TOTP(user.two_factor_secret)
            is_valid = totp.verify(token, valid_window=2)  # Allow ±60 seconds window
            
            if not is_valid:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'Invalid verification token'
                    }
                }, 400)
            
            # Enable 2FA
            user.two_factor_enabled = True
            user.save()
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': 'Two-factor authentication enabled successfully',
                'enabled': True
            })
            
        except Exception as e:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to verify 2FA token. Please try again.'
                }
            }, 500)
    
    def disable(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/two-factor/disable
        Disable 2FA with token or password verification
        """
        # Validate request
        errors = request.validate(
            validate.required(['token'])
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)
        
        if not user.two_factor_enabled:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Two-factor authentication is not enabled'
                }
            }, 400)
        
        try:
            token = request.input('token').replace(' ', '').strip()
            password = request.input('password', '')
            
            is_valid = False
            
            # Try password verification first if provided
            if password:
                is_valid = user.verify_password(password)
            else:
                # Verify TOTP token
                if user.two_factor_secret:
                    totp = pyotp.TOTP(user.two_factor_secret)
                    is_valid = totp.verify(token, valid_window=2)
            
            if not is_valid:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'Invalid token or password'
                    }
                }, 400)
              # Disable 2FA and clear recovery codes
            user.two_factor_enabled = False
            user.two_factor_secret = None
            user.save()
            
            # Clear recovery codes as per LoopBack behavior
            try:
                from app.services.RecoveryCodeService import RecoveryCodeService
                recovery_service = RecoveryCodeService()
                recovery_service.clear_recovery_codes(user.id)
                print(f"✅ Cleared recovery codes for user: {user.email}")
            except Exception as clear_error:
                print(f"⚠️ Failed to clear recovery codes: {clear_error}")
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': 'Two-factor authentication disabled successfully',
                'enabled': False
            })
            
        except Exception as e:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to disable 2FA. Please try again.'
                }
            }, 500)
    
    def recovery_codes(self, request: Request, response: Response):
        """
        GET /api/two-factor/recovery-codes
        Get recovery codes for 2FA - returns existing codes status (not regenerate)
        """
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)
        
        if not user.two_factor_enabled:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Two-factor authentication is not enabled'
                }
            }, 400)
        
        try:
            # Check current recovery codes status instead of generating new ones
            from app.services.RecoveryCodeService import RecoveryCodeService
            recovery_service = RecoveryCodeService()
            
            # Get remaining codes count
            remaining_codes = getattr(user, 'backup_codes_remaining', 0)
            
            # Return codes status (not actual codes for security)
            recovery_codes_info = []
            for i in range(recovery_service.TOTAL_CODES):
                field_name = f'backup_code_{i+1}'
                has_code = bool(getattr(user, field_name, None))
                if has_code:
                    recovery_codes_info.append(f"Recovery code #{i+1} available")
                else:
                    recovery_codes_info.append(f"Recovery code #{i+1} used")
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'recoveryCodes': recovery_codes_info,
                'remainingCodes': remaining_codes,
                'totalCodes': recovery_service.TOTAL_CODES,
                'message': f'You have {remaining_codes} recovery codes remaining'
            })
            
        except Exception as e:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get recovery codes status. Please try again.'
                }
            }, 500)
    
    def regenerate_codes(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/two-factor/regenerate-codes
        Regenerate recovery codes for 2FA
        """
        # Validate password or 2FA token for security
        errors = request.validate(
            validate.required(['password'])
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'Password verification required',
                    'details': errors.all()
                }
            }, 422)
        
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)
        
        if not user.two_factor_enabled:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Two-factor authentication is not enabled'
                }
            }, 400)
        
        try:
            password = request.input('password')
            
            # Verify password
            if not user.verify_password(password):
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'Invalid password'
                    }
                }, 400)
              # Generate new recovery codes using RecoveryCodeService
            from app.services.RecoveryCodeService import RecoveryCodeService
            recovery_service = RecoveryCodeService()
            backup_codes = recovery_service.generate_recovery_codes(user.id)
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'recoveryCodes': backup_codes,
                'message': 'Recovery codes regenerated successfully'
            })

        except Exception as e:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to regenerate recovery codes. Please try again.'
                }
            }, 500)

    def status(self, request: Request, response: Response):
        """
        GET /api/2fa/status
        Get 2FA status for current user - compatible with LoopBack
        """
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'enabled': user.two_factor_enabled or False,
            'methods': ['authenticator', 'sms', 'email'] if user.two_factor_enabled else []
        })

    def send_sms(self, request: Request, response: Response):
        """
        POST /api/2fa/send-sms
        Send 2FA code via SMS - compatible with LoopBack
        """
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        if not user.phone:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Phone number not configured'
                }
            }, 400)

        # Generate OTP for SMS
        import random
        otp = str(random.randint(100000, 999999))

        # Store OTP temporarily (in production, use Redis or database)
        # For now, return success message
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'SMS sent successfully'
        })

    def verify_sms(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/2fa/verify-sms
        Verify 2FA SMS code - compatible with LoopBack
        """
        errors = request.validate(
            validate.required(['code']),
            validate.string('code')
        )

        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # For now, return placeholder response
        # In production, verify against stored OTP
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'valid': False,
            'message': 'SMS verification not fully implemented'
        })

    def send_email(self, request: Request, response: Response):
        """
        POST /api/2fa/send-email
        Send 2FA code via email - compatible with LoopBack
        """
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # Generate OTP for email
        import random
        otp = str(random.randint(100000, 999999))

        # Store OTP temporarily (in production, use Redis or database)
        # For now, return success message
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'Email sent successfully'
        })

    def verify_email(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/2fa/verify-email
        Verify 2FA email code - compatible with LoopBack
        """
        errors = request.validate(
            validate.required(['code']),
            validate.string('code')
        )

        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # For now, return placeholder response
        # In production, verify against stored OTP
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'valid': False,
            'message': 'Email verification not fully implemented'
        })
