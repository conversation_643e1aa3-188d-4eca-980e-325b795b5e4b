"""
Recovery Code Service - LoopBack Compatible Implementation
Handles secure generation, storage, and validation of 2FA backup codes
"""

import secrets
import hashlib
import bcrypt
from datetime import datetime
from app.models.User import User


class RecoveryCodeService:
    """Recovery Code Service matching LoopBack implementation"""
    
    def __init__(self):
        self.SALT_ROUNDS = 12
        self.CODE_LENGTH = 8
        self.TOTAL_CODES = 3  # Same as Loop<PERSON>ack
    
    def generate_secure_code(self):
        """Generate a cryptographically secure recovery code"""
        # Generate 8 character alphanumeric code
        alphabet = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'  # Excludes confusing chars like 0, O, 1, I
        return ''.join(secrets.choice(alphabet) for _ in range(self.CODE_LENGTH))
    
    def generate_recovery_codes(self, user_id: str):
        """
        Generate new recovery codes for a user
        Returns array of plaintext codes (only time they're in plaintext)
        """
        try:
            print(f"🔑 Generating {self.TOTAL_CODES} recovery codes for user: {user_id}")
            
            # Get current user
            user = User.find(user_id)
            if not user:
                raise Exception(f"User not found: {user_id}")
            
            print(f"🔍 User found: {user.email}, current backup_codes_remaining: {getattr(user, 'backup_codes_remaining', 0)}")
            
            # Generate cryptographically secure codes
            codes = []
            hashed_codes = []
            
            for i in range(self.TOTAL_CODES):
                # Generate secure random code
                code = self.generate_secure_code()
                codes.append(code)
                
                # Hash the code for storage using bcrypt (same as LoopBack)
                hashed_code = bcrypt.hashpw(code.encode('utf-8'), bcrypt.gensalt(rounds=self.SALT_ROUNDS)).decode('utf-8')
                hashed_codes.append(hashed_code)
            
            print(f"🔍 About to update user with {len(hashed_codes)} hashed codes")
            print(f"🔍 Setting backup_codes_remaining to: {self.TOTAL_CODES}")
            
            # Store hashed codes in individual fields (LoopBack compatible)
            user.backup_code_1 = hashed_codes[0]
            user.backup_code_2 = hashed_codes[1]
            user.backup_code_3 = hashed_codes[2]
            user.backup_codes_remaining = self.TOTAL_CODES
            user.backup_codes_generated_at = datetime.now()
            user.updated_at = datetime.now()
            user.save()
            
            print(f"✅ Successfully stored {self.TOTAL_CODES} recovery codes")
            
            # Verify the update worked
            updated_user = User.find(user_id)
            print(f"🔍 Final verification - backup_codes_remaining: {updated_user.backup_codes_remaining}")
            print(f"🔍 Final verification - backup_code_1 exists: {bool(updated_user.backup_code_1)}")
            
            print(f"✅ Generated {self.TOTAL_CODES} recovery codes for user: {user_id}")
            
            return codes  # Return plaintext codes only once
            
        except Exception as error:
            print(f"❌ Error generating recovery codes: {error}")
            raise Exception('Failed to generate recovery codes')
    
    def validate_and_consume_recovery_code(self, user_id: str, input_code: str):
        """
        Validate and use a recovery code
        Returns True if code is valid and has been consumed
        """
        try:
            print(f"🔍 Validating recovery code for user: {user_id}")
            
            # Sanitize input
            clean_code = input_code.strip().upper()
            
            # Check code length
            if len(clean_code) != self.CODE_LENGTH:
                print(f"❌ Invalid recovery code length: {len(clean_code)}, expected: {self.CODE_LENGTH}")
                return False
            
            # Get user
            user = User.find(user_id)
            if not user:
                print(f"❌ User not found: {user_id}")
                return False
            
            # Check if user has any recovery codes remaining
            if not hasattr(user, 'backup_codes_remaining') or not user.backup_codes_remaining or user.backup_codes_remaining <= 0:
                print('❌ No recovery codes remaining')
                raise Exception('No recovery codes remaining. Please contact support.')
            
            # Check each available code
            code_fields = ['backup_code_1', 'backup_code_2', 'backup_code_3']
            matched_field = None
            
            for field in code_fields:
                hashed_code = getattr(user, field, None)
                if hashed_code:
                    # Use bcrypt to compare (same as LoopBack)
                    is_match = bcrypt.checkpw(clean_code.encode('utf-8'), hashed_code.encode('utf-8'))
                    if is_match:
                        matched_field = field
                        break
            
            if not matched_field:
                print('❌ Recovery code does not match any stored codes')
                return False
            
            # Remove the used code and update remaining count
            setattr(user, matched_field, None)
            user.backup_codes_remaining = user.backup_codes_remaining - 1
            user.updated_at = datetime.now()
            user.save()
            
            print(f"✅ Recovery code validated and consumed for user: {user_id}")
            print(f"📊 Remaining codes: {user.backup_codes_remaining}")
            
            return True
            
        except Exception as error:
            print(f"❌ Recovery code validation failed for user: {user_id}: {error}")
            if 'No recovery codes remaining' in str(error):
                raise error
            return False
    
    def are_all_codes_used(self, user_id: str):
        """
        Check if all recovery codes have been used
        Returns true if all codes are used (no remaining codes)
        """
        try:
            user = User.find(user_id)
            if not user:
                return True  # If user doesn't exist, consider all codes used
            
            # Check if backup_codes_remaining is 0 or None
            remaining = user.backup_codes_remaining
            if remaining is None or remaining <= 0:
                print(f"🔍 All recovery codes used for user {user_id}: remaining={remaining}")
                return True
            
            print(f"🔍 Recovery codes available for user {user_id}: remaining={remaining}")
            return False
            
        except Exception as error:
            print(f"❌ Error checking recovery code status for user {user_id}: {error}")
            return True  # Default to "all codes used" on error for security
    
    def clear_recovery_codes(self, user_id: str):
        """Clear all recovery codes for a user"""
        try:
            user = User.find(user_id)
            if not user:
                return
            
            user.backup_code_1 = None
            user.backup_code_2 = None
            user.backup_code_3 = None
            user.backup_codes_remaining = 0
            user.backup_codes_generated_at = None
            user.updated_at = datetime.now()
            user.save()
            
            print(f"✅ Cleared all recovery codes for user: {user_id}")
            
        except Exception as error:
            print(f"❌ Failed to clear recovery codes for user: {user_id}: {error}")
