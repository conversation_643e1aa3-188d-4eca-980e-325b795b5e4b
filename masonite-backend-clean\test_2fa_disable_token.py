#!/usr/bin/env python3
"""
Test script to verify 2FA disable token generation and validation
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__)))

from masonite.environment import env
from datetime import datetime, timedelta
import jwt

def test_token_generation():
    """Test token generation for 2FA disable"""
    
    # Simulate user data
    user_data = {
        'user_id': 16,
        'email': '<EMAIL>',
        'action': 'disable_2fa',
        'reason': 'lost_device',
        'exp': (datetime.utcnow() + timedelta(hours=1)).timestamp()
    }
    
    # Generate token
    token = jwt.encode(
        user_data,
        env('JWT_SECRET', 'default-secret-key'),
        algorithm='HS256'
    )
    
    print(f"Generated token: {token}")
    
    # Verify token
    try:
        decoded = jwt.decode(
            token,
            env('JWT_SECRET', 'default-secret-key'),
            algorithms=['HS256']
        )
        print(f"Decoded payload: {decoded}")
        print("✅ Token validation successful")
        return token
    except Exception as e:
        print(f"❌ Token validation failed: {e}")
        return None

def test_confirm_endpoint(token):
    """Test the confirm disable 2FA endpoint"""
    import requests
    
    url = "http://localhost:3002/api/auth/confirm-disable-2fa"
    data = {"token": token}
    
    try:
        response = requests.post(url, json=data, timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    print("🔐 Testing 2FA disable token functionality...")
    
    # Generate and test token
    token = test_token_generation()
    
    if token:
        print(f"\n🧪 Testing confirm endpoint...")
        test_confirm_endpoint(token)
